"use client";

import { useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import * as Select from "@radix-ui/react-select";
import * as Tooltip from "@radix-ui/react-tooltip";

import type { Strategy } from "@/types/fund";

interface ParameterInputsProperties {
  strategy: Strategy;
  parameters: Record<string, unknown>;
  onParametersChange: (parameters: Record<string, unknown>) => void;
}

export default function ParameterInputs({
  strategy,
  parameters,
  onParametersChange,
}: ParameterInputsProperties) {
  const {
    control,
    watch,
    formState: { errors },
    setValue,
    reset,
  } = useForm({
    mode: "onChange",
    defaultValues: parameters,
  });

  // 监听表单变化并通知父组件
  const watchedValues = watch();

  useEffect(() => {
    onParametersChange(watchedValues);
  }, [watchedValues, onParametersChange]);

  // 初始化表单默认值
  useEffect(() => {
    const initialParameters: Record<string, unknown> = {};
    for (const [key, parameter] of Object.entries(strategy.parameterSchema)) {
      initialParameters[key] = parameters[key] ?? parameter.defaultValue ?? "";
    }
    reset(initialParameters);
  }, [strategy.id, parameters, strategy.parameterSchema, reset]);

  // 验证规则生成器
  const getValidationRules = (parameter: Strategy["parameterSchema"][string]) => {
    const rules: any = {};

    if (parameter.required) {
      rules.required = `${parameter.label}不能为空`;
    }

    if (parameter.type === "number") {
      rules.validate = (value: string) => {
        if (!value) return true; // 空值由required规则处理
        const numberValue = Number(value);
        if (Number.isNaN(numberValue)) {
          return `${parameter.label}必须是数字`;
        }
        if (parameter.min !== undefined && numberValue < parameter.min) {
          return `${parameter.label}不能小于${parameter.min}`;
        }
        if (parameter.max !== undefined && numberValue > parameter.max) {
          return `${parameter.label}不能大于${parameter.max}`;
        }
        return true;
      };
    }

    return rules;
  };

  // 渲染不同类型的输入组件
  const renderInput = (
    key: string,
    parameter: Strategy["parameterSchema"][string]
  ) => {
    const error = errors[key];
    const baseInputClasses = `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
      error ? "border-red-300" : "border-gray-300"
    }`;

    return (
      <Controller
        name={key}
        control={control}
        rules={getValidationRules(parameter)}
        render={({ field }) => {
          switch (parameter.type) {
            case "number": {
              return (
                <input
                  {...field}
                  type="number"
                  min={parameter.min}
                  max={parameter.max}
                  step={parameter.step}
                  className={baseInputClasses}
                  placeholder={`请输入${parameter.label}`}
                />
              );
            }

            case "date": {
              return (
                <input
                  {...field}
                  type="date"
                  className={baseInputClasses}
                />
              );
            }

            case "select": {
              return (
                <Select.Root value={field.value} onValueChange={field.onChange}>
                  <Select.Trigger
                    className={`${baseInputClasses} flex items-center justify-between`}
                  >
                    <Select.Value placeholder={`请选择${parameter.label}`} />
                    <Select.Icon>
                      <svg
                        className="h-4 w-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </Select.Icon>
                  </Select.Trigger>
                  <Select.Portal>
                    <Select.Content className="bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-60 overflow-auto">
                      <Select.Viewport className="p-1">
                        {parameter.options?.map((option) => (
                          <Select.Item
                            key={option.value}
                            value={option.value}
                            className="relative flex items-center px-3 py-2 text-sm rounded cursor-pointer hover:bg-blue-50 focus:bg-blue-50 focus:outline-none data-[highlighted]:bg-blue-50"
                          >
                            <Select.ItemText>{option.label}</Select.ItemText>
                            <Select.ItemIndicator className="absolute right-2">
                              <svg
                                className="h-4 w-4 text-blue-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                            </Select.ItemIndicator>
                          </Select.Item>
                        ))}
                      </Select.Viewport>
                    </Select.Content>
                  </Select.Portal>
                </Select.Root>
              );
            }

            case "range": {
              return (
                <div className="space-y-2">
                  <input
                    {...field}
                    type="range"
                    min={parameter.min}
                    max={parameter.max}
                    step={parameter.step}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>{parameter.min}</span>
                    <span className="font-medium">{field.value || parameter.defaultValue}</span>
                    <span>{parameter.max}</span>
                  </div>
                </div>
              );
            }

            default: {
              return (
                <input
                  {...field}
                  type="text"
                  className={baseInputClasses}
                  placeholder={`请输入${parameter.label}`}
                />
              );
            }
          }
        }}
      />
    );
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">策略参数设置</h3>
      {Object.entries(strategy.parameterSchema).map(([key, parameter]) => (
        <div key={key} className="space-y-1">
          <label className="block text-sm font-medium text-gray-700">
            {parameter.label}
            {parameter.required && (
              <span className="text-red-500 ml-1">*</span>
            )}
          </label>
          {renderInput(key, parameter)}
          {errors[key] && (
            <p className="text-sm text-red-500 mt-1">{errors[key]?.message}</p>
          )}
          {parameter.description && (
            <p className="text-xs text-gray-500 mt-1">{parameter.description}</p>
          )}
        </div>
      ))}
    </div>
  );
}


