"use client";

import { useState } from "react";
import FundSelector from "@/components/fund-selector";
import IndexSelector from "@/components/index-selector";
import StrategySelector from "@/components/strategy-selector";
import ParameterInputs from "@/components/parameter-inputs";
import type { Fund, Strategy, IndexType } from "@/types/fund";

interface BacktestFormProps {
  onBacktestRun?: (params: {
    fund: Fund;
    index?: IndexType;
    strategy: Strategy;
    parameters: Record<string, unknown>;
  }) => void;
}

export default function BacktestForm({ onBacktestRun }: BacktestFormProps) {
  const [selectedFund, setSelectedFund] = useState<Fund | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<IndexType | null>(null);
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null);
  const [parameters, setParameters] = useState<Record<string, unknown>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleRunBacktest = async () => {
    if (!selectedFund || !selectedStrategy) {
      setError("请选择基金和投资策略");
      return;
    }

    // 验证必填参数
    const requiredParameters = Object.entries(selectedStrategy.parameterSchema)
      .filter(([, parameter]) => parameter.required)
      .map(([key]) => key);

    const missingParameters = requiredParameters.filter(
      (key) => !parameters[key] || parameters[key] === ""
    );
    
    if (missingParameters.length > 0) {
      setError("请填写所有必填参数");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      if (onBacktestRun) {
        await onBacktestRun({
          fund: selectedFund,
          index: selectedIndex || undefined,
          strategy: selectedStrategy,
          parameters,
        });
      }
    } catch (error_) {
      console.error("回测执行失败:", error_);
      setError("回测执行失败，请检查参数设置");
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setSelectedFund(null);
    setSelectedIndex(null);
    setSelectedStrategy(null);
    setParameters({});
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* 基金搜索 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <FundSelector
          selectedFund={selectedFund}
          onFundSelect={setSelectedFund}
        />
      </div>

      {/* 指数选择 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <IndexSelector
          selectedIndex={selectedIndex}
          onIndexSelect={setSelectedIndex}
        />
      </div>

      {/* 策略选择 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <StrategySelector
          selectedStrategy={selectedStrategy}
          onStrategySelect={setSelectedStrategy}
        />
      </div>

      {/* 参数设置 */}
      {selectedStrategy && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <ParameterInputs
            strategy={selectedStrategy}
            parameters={parameters}
            onParametersChange={setParameters}
          />
        </div>
      )}

      {/* 执行回测按钮 */}
      {selectedFund && selectedStrategy && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex space-x-4">
            <button
              onClick={handleRunBacktest}
              disabled={isLoading}
              className={`flex-1 py-3 px-4 rounded-lg font-medium text-white transition-colors ${
                isLoading
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              }`}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  执行回测中...
                </div>
              ) : (
                "开始回测"
              )}
            </button>

            <button
              onClick={resetForm}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              重置
            </button>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 text-red-400 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm text-red-800">{error}</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
