import { Suspense } from "react";
import BacktestForm from "@/components/backtest-form";
import BacktestResults from "@/components/backtest-results";

export default function BacktestPage() {
  return (
    <>
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                基金投资策略回测计算器
              </h1>
              <p className="mt-2 text-gray-600">
                选择基金和投资策略，分析历史表现
              </p>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧配置面板 */}
          <div className="lg:col-span-1">
            <Suspense fallback={<div className="animate-pulse bg-gray-200 h-96 rounded-lg" />}>
              <BacktestForm />
            </Suspense>
          </div>

          {/* 右侧结果展示 */}
          <div className="lg:col-span-2">
            <Suspense fallback={<div className="animate-pulse bg-gray-200 h-96 rounded-lg" />}>
              <BacktestResults />
            </Suspense>
          </div>
        </div>
      </main>
    </>
  );
}
